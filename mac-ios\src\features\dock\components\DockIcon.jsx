import { memo, useRef, useEffect, useState } from 'react';
import styled from 'styled-components';
import { useDockStore, useWindowStore } from '../../../store';

/**
 * Styled components for dock icons
 */
const IconContainer = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: bottom center;
  
  /* Magnification effect */
  transform: scale(${props => props.scale || 1}) translateY(${props => (props.scale - 1) * -10}px);
`;

const IconWrapper = styled.div`
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
  background: ${props => props.gradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.15);
  color: white;
  font-size: 1.25rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  /* Frosted glass effect */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: inherit;
  }

  /* Hover glow effect */
  &::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: ${props => props.gradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};
    border-radius: calc(1rem + 2px);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    filter: blur(8px);
  }

  ${IconContainer}:hover &::after {
    opacity: 0.3;
  }

  @media (max-width: 768px) {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
`;

const RunningIndicator = styled.div`
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0.25rem;
  height: 0.25rem;
  background: white;
  border-radius: 50%;
  opacity: ${props => props.visible ? 1 : 0};
  transition: all 0.3s ease;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);

  @media (max-width: 768px) {
    bottom: -0.375rem;
    width: 0.1875rem;
    height: 0.1875rem;
  }
`;

const Tooltip = styled.div`
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: ${props => props.theme?.spacing?.xs || '0.25rem'} ${props => props.theme?.spacing?.sm || '0.5rem'};
  border-radius: ${props => props.theme?.borderRadius?.sm || '0.375rem'};
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: ${props => props.visible ? 1 : 0};
  pointer-events: none;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
  }
`;

/**
 * DockIcon component with magnification and running indicators
 * 
 * @param {Object} props
 * @param {Object} props.app - App data object
 * @param {number} props.index - Icon index in dock
 * @param {Function} props.onClick - Click handler
 * @param {Function} props.onHover - Hover handler
 * @param {Function} props.calculateMagnification - Magnification calculator
 * @param {Object} props.mousePosition - Current mouse position
 */
const DockIcon = memo(({ 
  app, 
  index, 
  onClick, 
  onHover, 
  calculateMagnification, 
  mousePosition 
}) => {
  const iconRef = useRef(null);
  const [scale, setScale] = useState(1);
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipTimer, setTooltipTimer] = useState(null);
  
  const { windows } = useWindowStore();
  
  const IconComponent = app.icon;
  
  // Check if app is running (has open windows)
  const isRunning = windows.some(window => window.appId === app.id && !window.minimized);

  // Calculate magnification effect
  useEffect(() => {
    if (!iconRef.current || !calculateMagnification) return;
    
    const rect = iconRef.current.getBoundingClientRect();
    const newScale = calculateMagnification(rect);
    setScale(newScale);
  }, [mousePosition, calculateMagnification]);

  // Handle mouse enter for tooltip
  const handleMouseEnter = () => {
    onHover(app.id);
    
    // Show tooltip after delay
    const timer = setTimeout(() => {
      setShowTooltip(true);
    }, 1000);
    
    setTooltipTimer(timer);
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    onHover(null);
    setShowTooltip(false);
    
    if (tooltipTimer) {
      clearTimeout(tooltipTimer);
      setTooltipTimer(null);
    }
  };

  // Handle click with animation
  const handleClick = () => {
    // Add click animation
    setScale(0.9);
    setTimeout(() => {
      setScale(1);
    }, 150);
    
    onClick(app);
  };

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (tooltipTimer) {
        clearTimeout(tooltipTimer);
      }
    };
  }, [tooltipTimer]);

  return (
    <IconContainer
      ref={iconRef}
      scale={scale}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
    >
      <IconWrapper gradient={app.color}>
        <IconComponent />
      </IconWrapper>
      
      <RunningIndicator visible={isRunning} />
      
      <Tooltip visible={showTooltip}>
        {app.name}
      </Tooltip>
    </IconContainer>
  );
});

DockIcon.displayName = 'DockIcon';

export default DockIcon;
