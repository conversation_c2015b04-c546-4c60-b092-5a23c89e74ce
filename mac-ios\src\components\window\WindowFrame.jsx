import { useRef } from 'react';
import styled from 'styled-components';
import { useWindowStore } from '../../store';
import { IconButton, Flex, Text } from '../../styles/components';
import { useDraggable } from '../../hooks/useDraggable';

/**
 * Styled components for the window frame
 */
const WindowContainer = styled.div`
  position: absolute;
  border-radius: ${props => props.theme?.borderRadius?.lg || '0.75rem'};
  overflow: hidden;
  box-shadow: ${props => props.theme?.shadows?.xl || '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'};
  background: ${props => props.theme?.colors?.surface || '#F2F2F7'};
  border: 1px solid ${props => props.theme?.colors?.border || '#C6C6C8'};
  transition: all 0.2s ease-in-out;
  min-width: 300px;
  min-height: 200px;

  ${props => props.isActive && `
    box-shadow: 0 0 0 2px ${props.theme?.colors?.primary || '#007AFF'}44, ${props.theme?.shadows?.xl || '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'};
  `}

  ${props => !props.isActive && `
    opacity: 0.95;
  `}
`;

const WindowHeader = styled.div`
  height: 2rem;
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  display: flex;
  align-items: center;
  padding: 0 ${props => props.theme.spacing.md};
  cursor: move;
  user-select: none;
  position: relative;

  &:active {
    cursor: grabbing;
  }
`;

const WindowControls = styled(Flex)`
  position: absolute;
  left: ${props => props.theme.spacing.sm};
  top: 50%;
  transform: translateY(-50%);
  gap: ${props => props.theme.spacing.xs};
`;

const WindowTitle = styled(Text)`
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-weight: 500;
  font-size: 0.875rem;
  color: ${props => props.theme.colors.text};
  pointer-events: none;
`;

const WindowContent = styled.div`
  height: calc(100% - 2rem);
  background: ${props => props.theme.colors.background};
  overflow: auto;
  position: relative;
`;

const ControlButton = styled(IconButton)`
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: scale(1.1);
  }
`;

const CloseButton = styled(ControlButton)`
  background: #ff5f57;

  &:hover {
    background: #ff4136;
  }
`;

const MinimizeButton = styled(ControlButton)`
  background: #ffbd2e;

  &:hover {
    background: #ff9500;
  }
`;

const MaximizeButton = styled(ControlButton)`
  background: #28ca42;

  &:hover {
    background: #34c759;
  }
`;

/**
 * WindowFrame component with draggable functionality and window controls
 *
 * @param {Object} props
 * @param {string} props.windowId - The unique ID of the window
 * @param {React.ReactNode} props.children - The content of the window
 */
const WindowFrame = ({ windowId, children }) => {
  const windowRef = useRef(null);
  const headerRef = useRef(null);

  const {
    getWindowById,
    closeWindow,
    minimizeWindow,
    maximizeWindow,
    bringToFront,
    updateWindowPosition,
    activeWindow,
    setDragging
  } = useWindowStore();

  const window = getWindowById(windowId);
  const isActive = activeWindow === windowId;

  // Custom hook for draggable functionality
  const { isDragging } = useDraggable({
    elementRef: windowRef,
    handleRef: headerRef,
    onDragStart: () => {
      bringToFront(windowId);
      setDragging(true, windowId);
    },
    onDrag: (position) => {
      updateWindowPosition(windowId, position);
    },
    onDragEnd: () => {
      setDragging(false);
    },
    disabled: !window || window.maximized
  });

  // Handle window click to bring to front
  const handleWindowClick = () => {
    if (!isActive) {
      bringToFront(windowId);
    }
  };

  // Handle control button clicks
  const handleClose = (e) => {
    e.stopPropagation();
    closeWindow(windowId);
  };

  const handleMinimize = (e) => {
    e.stopPropagation();
    minimizeWindow(windowId);
  };

  const handleMaximize = (e) => {
    e.stopPropagation();
    maximizeWindow(windowId);
  };

  if (!window || window.minimized) {
    return null;
  }

  return (
    <WindowContainer
      ref={windowRef}
      isActive={isActive}
      onClick={handleWindowClick}
      style={{
        left: window.position.x,
        top: window.position.y,
        width: window.size.width,
        height: window.size.height,
        zIndex: window.zIndex,
        cursor: isDragging ? 'grabbing' : 'default'
      }}
    >
      <WindowHeader ref={headerRef}>
        <WindowControls>
          <CloseButton onClick={handleClose} aria-label="Close window" />
          <MinimizeButton onClick={handleMinimize} aria-label="Minimize window" />
          <MaximizeButton onClick={handleMaximize} aria-label="Maximize window" />
        </WindowControls>
        <WindowTitle>{window.title}</WindowTitle>
      </WindowHeader>
      <WindowContent>
        {children}
      </WindowContent>
    </WindowContainer>
  );
};

export default WindowFrame;
