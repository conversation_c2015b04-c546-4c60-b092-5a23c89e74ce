import { create } from 'zustand';
import { persist, subscribeWithSelector } from 'zustand/middleware';

/**
 * Theme mode: 'light' | 'dark' | 'auto'
 */

/**
 * Theme colors shape:
 * {
 *   primary: string,
 *   secondary: string,
 *   background: string,
 *   surface: string,
 *   text: string,
 *   textSecondary: string,
 *   border: string,
 *   accent: string,
 *   success: string,
 *   warning: string,
 *   error: string,
 *   glass: string,
 *   glassStrong: string
 * }
 */

/**
 * Theme configuration shape:
 * {
 *   mode: ThemeMode,
 *   colors: ThemeColors,
 *   fonts: { system: string, mono: string },
 *   spacing: { xs: string, sm: string, md: string, lg: string, xl: string },
 *   borderRadius: { sm: string, md: string, lg: string, xl: string },
 *   shadows: { sm: string, md: string, lg: string, xl: string }
 * }
 */

/**
 * Theme store shape:
 * {
 *   theme: ThemeConfig,
 *   systemPrefersDark: boolean,
 *   // Actions
 *   setThemeMode: (mode) => void,
 *   toggleTheme: () => void,
 *   setSystemPrefersDark: (prefersDark) => void,
 *   getCurrentTheme: () => ThemeConfig
 * }
 */

// Light theme colors
const lightColors = {
  primary: '#007AFF',
  secondary: '#5856D6',
  background: '#FFFFFF',
  surface: '#F2F2F7',
  text: '#000000',
  textSecondary: '#6D6D70',
  border: '#C6C6C8',
  accent: '#FF9500',
  success: '#34C759',
  warning: '#FF9500',
  error: '#FF3B30',
  glass: 'rgba(255, 255, 255, 0.8)',
  glassStrong: 'rgba(255, 255, 255, 0.95)'
};

// Dark theme colors
const darkColors = {
  primary: '#0A84FF',
  secondary: '#5E5CE6',
  background: '#000000',
  surface: '#1C1C1E',
  text: '#FFFFFF',
  textSecondary: '#AEAEB2',
  border: '#38383A',
  accent: '#FF9F0A',
  success: '#30D158',
  warning: '#FF9F0A',
  error: '#FF453A',
  glass: 'rgba(0, 0, 0, 0.8)',
  glassStrong: 'rgba(0, 0, 0, 0.95)'
};

// Base theme configuration
const baseTheme = {
  fonts: {
    system: 'Inter, -apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
    mono: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace'
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem'
  },
  borderRadius: {
    sm: '0.375rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem'
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
  }
};

const useThemeStore = create(
  persist(
    subscribeWithSelector((set, get) => ({
      theme: {
        mode: 'auto',
        colors: lightColors,
        ...baseTheme
      },
      systemPrefersDark: false,

      setThemeMode: (mode) => {
        const { systemPrefersDark } = get();
        const isDark = mode === 'dark' || (mode === 'auto' && systemPrefersDark);

        set(state => ({
          theme: {
            ...state.theme,
            mode,
            colors: isDark ? darkColors : lightColors
          }
        }));
      },

      toggleTheme: () => {
        const { theme } = get();
        const newMode = theme.mode === 'light' ? 'dark' : 'light';
        get().setThemeMode(newMode);
      },

      setSystemPrefersDark: (prefersDark) => {
        set({ systemPrefersDark: prefersDark });

        const { theme } = get();
        if (theme.mode === 'auto') {
          get().setThemeMode('auto'); // Trigger theme update
        }
      },

      getCurrentTheme: () => {
        const state = get();
        return state?.theme || {
          mode: 'auto',
          colors: lightColors,
          ...baseTheme
        };
      }
    })),
    {
      name: 'macos-theme-storage',
      partialize: (state) => ({ 
        theme: { mode: state.theme.mode },
        systemPrefersDark: state.systemPrefersDark 
      })
    }
  )
);

export default useThemeStore;
