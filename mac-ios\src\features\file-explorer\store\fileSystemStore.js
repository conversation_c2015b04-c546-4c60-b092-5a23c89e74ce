import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

/**
 * Mock file system data
 * Each item represents a file or folder with properties:
 * - id: unique identifier
 * - name: display name
 * - type: 'file' or 'folder'
 * - path: full path
 * - parentId: parent folder id (null for root)
 * - size: file size (optional)
 * - modified: last modified date (optional)
 * - created: creation date (optional)
 * - content: file content for text files (optional)
 */
const mockFileSystem = [
  // Root folders
  { id: 'desktop', name: 'Desktop', type: 'folder', path: '/Desktop', parentId: null, created: '2024-01-01', modified: '2024-01-15' },
  { id: 'documents', name: 'Documents', type: 'folder', path: '/Documents', parentId: null, created: '2024-01-01', modified: '2024-01-10' },
  { id: 'downloads', name: 'Downloads', type: 'folder', path: '/Downloads', parentId: null, created: '2024-01-01', modified: '2024-01-20' },
  { id: 'pictures', name: 'Pictures', type: 'folder', path: '/Pictures', parentId: null, created: '2024-01-01', modified: '2024-01-12' },
  { id: 'music', name: 'Music', type: 'folder', path: '/Music', parentId: null, created: '2024-01-01', modified: '2024-01-08' },
  { id: 'videos', name: 'Videos', type: 'folder', path: '/Videos', parentId: null, created: '2024-01-01', modified: '2024-01-05' },
  
  // Desktop files
  { id: 'readme', name: 'README.txt', type: 'file', path: '/Desktop/README.txt', parentId: 'desktop', size: '2.1 KB', created: '2024-01-15', modified: '2024-01-15', content: 'Welcome to macOS Demo!\n\nThis is a demonstration of a macOS-style operating system built with React.' },
  { id: 'project', name: 'Project.zip', type: 'file', path: '/Desktop/Project.zip', parentId: 'desktop', size: '15.3 MB', created: '2024-01-14', modified: '2024-01-14' },
  
  // Documents files
  { id: 'report', name: 'Annual Report.docx', type: 'file', path: '/Documents/Annual Report.docx', parentId: 'documents', size: '1.8 MB', created: '2024-01-10', modified: '2024-01-10' },
  { id: 'notes', name: 'Meeting Notes.txt', type: 'file', path: '/Documents/Meeting Notes.txt', parentId: 'documents', size: '856 B', created: '2024-01-09', modified: '2024-01-09', content: 'Meeting Notes - January 9, 2024\n\n- Discussed project timeline\n- Reviewed budget allocation\n- Next meeting scheduled for January 16' },
  { id: 'presentation', name: 'Presentation.pptx', type: 'file', path: '/Documents/Presentation.pptx', parentId: 'documents', size: '5.2 MB', created: '2024-01-08', modified: '2024-01-08' },
  
  // Downloads files
  { id: 'installer', name: 'app-installer.dmg', type: 'file', path: '/Downloads/app-installer.dmg', parentId: 'downloads', size: '125.7 MB', created: '2024-01-20', modified: '2024-01-20' },
  { id: 'image1', name: 'wallpaper.jpg', type: 'file', path: '/Downloads/wallpaper.jpg', parentId: 'downloads', size: '3.4 MB', created: '2024-01-19', modified: '2024-01-19' },
  { id: 'document', name: 'invoice.pdf', type: 'file', path: '/Downloads/invoice.pdf', parentId: 'downloads', size: '245 KB', created: '2024-01-18', modified: '2024-01-18' },
  
  // Pictures files
  { id: 'photo1', name: 'vacation-2024.jpg', type: 'file', path: '/Pictures/vacation-2024.jpg', parentId: 'pictures', size: '4.2 MB', created: '2024-01-12', modified: '2024-01-12' },
  { id: 'photo2', name: 'family-portrait.png', type: 'file', path: '/Pictures/family-portrait.png', parentId: 'pictures', size: '8.1 MB', created: '2024-01-11', modified: '2024-01-11' },
  { id: 'screenshot', name: 'screenshot.png', type: 'file', path: '/Pictures/screenshot.png', parentId: 'pictures', size: '1.3 MB', created: '2024-01-10', modified: '2024-01-10' },
  
  // Music files
  { id: 'song1', name: 'favorite-song.mp3', type: 'file', path: '/Music/favorite-song.mp3', parentId: 'music', size: '7.8 MB', created: '2024-01-08', modified: '2024-01-08' },
  { id: 'album', name: 'album-collection.zip', type: 'file', path: '/Music/album-collection.zip', parentId: 'music', size: '156.2 MB', created: '2024-01-07', modified: '2024-01-07' },
  
  // Videos files
  { id: 'video1', name: 'demo-video.mp4', type: 'file', path: '/Videos/demo-video.mp4', parentId: 'videos', size: '89.5 MB', created: '2024-01-05', modified: '2024-01-05' },
  { id: 'tutorial', name: 'tutorial.mov', type: 'file', path: '/Videos/tutorial.mov', parentId: 'videos', size: '234.1 MB', created: '2024-01-04', modified: '2024-01-04' },
  
  // Nested folders
  { id: 'work-folder', name: 'Work Projects', type: 'folder', path: '/Documents/Work Projects', parentId: 'documents', created: '2024-01-05', modified: '2024-01-09' },
  { id: 'work-file1', name: 'project-alpha.docx', type: 'file', path: '/Documents/Work Projects/project-alpha.docx', parentId: 'work-folder', size: '2.3 MB', created: '2024-01-09', modified: '2024-01-09' },
  { id: 'work-file2', name: 'budget-2024.xlsx', type: 'file', path: '/Documents/Work Projects/budget-2024.xlsx', parentId: 'work-folder', size: '1.1 MB', created: '2024-01-08', modified: '2024-01-08' },
];

const useFileSystemStore = create(
  subscribeWithSelector((set, get) => ({
    currentPath: '/Desktop',
    files: mockFileSystem,
    selectedFiles: [],
    viewMode: 'grid',
    searchQuery: '',
    history: ['/Desktop'],
    historyIndex: 0,

    navigateToPath: (path) => {
      const { history, historyIndex } = get();

      // Add to history if it's a new path
      if (history[historyIndex] !== path) {
        const newHistory = history.slice(0, historyIndex + 1);
        newHistory.push(path);

        set({
          currentPath: path,
          selectedFiles: [],
          history: newHistory,
          historyIndex: newHistory.length - 1
        });
      } else {
        set({
          currentPath: path,
          selectedFiles: []
        });
      }
    },

    navigateBack: () => {
      const { history, historyIndex } = get();
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        set({
          currentPath: history[newIndex],
          selectedFiles: [],
          historyIndex: newIndex
        });
      }
    },

    navigateForward: () => {
      const { history, historyIndex } = get();
      if (historyIndex < history.length - 1) {
        const newIndex = historyIndex + 1;
        set({
          currentPath: history[newIndex],
          selectedFiles: [],
          historyIndex: newIndex
        });
      }
    },

    selectFile: (fileId, multiSelect = false) => {
      set(state => {
        if (multiSelect) {
          const isSelected = state.selectedFiles.includes(fileId);
          return {
            selectedFiles: isSelected
              ? state.selectedFiles.filter(id => id !== fileId)
              : [...state.selectedFiles, fileId]
          };
        } else {
          return {
            selectedFiles: [fileId]
          };
        }
      });
    },

    deselectAll: () => {
      set({ selectedFiles: [] });
    },

    setViewMode: (mode) => {
      set({ viewMode: mode });
    },

    setSearchQuery: (query) => {
      set({ searchQuery: query });
    },

    createFile: (name, content = '') => {
      const { currentPath, files } = get();
      const parentFolder = files.find(f => f.path === currentPath && f.type === 'folder');

      const newFile = {
        id: `file-${Date.now()}`,
        name,
        type: 'file',
        path: `${currentPath}/${name}`,
        parentId: parentFolder?.id || null,
        size: content ? `${content.length} B` : '0 B',
        created: new Date().toISOString().split('T')[0],
        modified: new Date().toISOString().split('T')[0],
        content
      };

      set(state => ({
        files: [...state.files, newFile]
      }));
    },

    createFolder: (name) => {
      const { currentPath, files } = get();
      const parentFolder = files.find(f => f.path === currentPath && f.type === 'folder');

      const newFolder = {
        id: `folder-${Date.now()}`,
        name,
        type: 'folder',
        path: `${currentPath}/${name}`,
        parentId: parentFolder?.id || null,
        created: new Date().toISOString().split('T')[0],
        modified: new Date().toISOString().split('T')[0]
      };

      set(state => ({
        files: [...state.files, newFolder]
      }));
    },

    deleteFile: (fileId) => {
      set(state => ({
        files: state.files.filter(f => f.id !== fileId && f.parentId !== fileId),
        selectedFiles: state.selectedFiles.filter(id => id !== fileId)
      }));
    },

    renameFile: (fileId, newName) => {
      set(state => ({
        files: state.files.map(file => {
          if (file.id === fileId) {
            const pathParts = file.path.split('/');
            pathParts[pathParts.length - 1] = newName;
            return {
              ...file,
              name: newName,
              path: pathParts.join('/'),
              modified: new Date().toISOString().split('T')[0]
            };
          }
          return file;
        })
      }));
    },

    moveFile: (fileId, newPath) => {
      set(state => ({
        files: state.files.map(file =>
          file.id === fileId
            ? { ...file, path: newPath, modified: new Date().toISOString().split('T')[0] }
            : file
        )
      }));
    },

    copyFile: (fileId, newPath) => {
      const { files } = get();
      const originalFile = files.find(f => f.id === fileId);

      if (originalFile) {
        const newFile = {
          ...originalFile,
          id: `copy-${Date.now()}`,
          path: newPath,
          created: new Date().toISOString().split('T')[0],
          modified: new Date().toISOString().split('T')[0]
        };

        set(state => ({
          files: [...state.files, newFile]
        }));
      }
    },

    getFilesByPath: (path) => {
      const { files } = get();
      return files.filter(file => {
        const filePath = file.path.substring(0, file.path.lastIndexOf('/')) || '/';
        return filePath === path;
      });
    },

    getFileById: (fileId) => {
      const { files } = get();
      return files.find(file => file.id === fileId);
    }
  }))
);

export { useFileSystemStore };
export default useFileSystemStore;
