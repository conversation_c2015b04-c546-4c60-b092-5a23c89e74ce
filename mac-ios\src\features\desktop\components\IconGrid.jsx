import { memo, useCallback } from 'react';
import styled from 'styled-components';
import { useDesktopStore, useWindowStore, useDockStore } from '../../../store';
import DesktopIcon from './DesktopIcon';

/**
 * Styled components for the icon grid
 */
const GridContainer = styled.div`
  position: absolute;
  top: 2rem;
  left: 0;
  right: 0;
  bottom: 5rem;
  padding: ${props => props.theme?.spacing?.lg || '1.5rem'};
  display: grid;
  grid-template-columns: repeat(auto-fill, 5rem);
  grid-template-rows: repeat(auto-fill, 5rem);
  gap: ${props => props.theme?.spacing?.md || '1rem'};
  align-content: start;
  justify-content: start;
  overflow: hidden;
  pointer-events: none;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, 4rem);
    grid-template-rows: repeat(auto-fill, 4rem);
    gap: ${props => props.theme?.spacing?.sm || '0.5rem'};
    padding: ${props => props.theme?.spacing?.md || '1rem'};
  }
`;

/**
 * IconGrid component with virtualized rendering for performance
 * Displays desktop icons in a grid layout
 */
const IconGrid = memo(() => {
  const { icons, selectIcon, deselectAll } = useDesktopStore();
  const { openWindow } = useWindowStore();
  const { setAppRunning, getAppById } = useDockStore();

  // Handle icon double-click to open app
  const handleIconOpen = useCallback((iconId) => {
    const app = getAppById(iconId);
    if (app) {
      openWindow(iconId, app.name);
      setAppRunning(iconId, true);
    }
  }, [getAppById, openWindow, setAppRunning]);

  // Handle icon selection
  const handleIconSelect = useCallback((iconId, multiSelect = false) => {
    selectIcon(iconId, multiSelect);
  }, [selectIcon]);

  // Handle background click to deselect all
  const handleBackgroundClick = useCallback((e) => {
    if (e.target === e.currentTarget) {
      deselectAll();
    }
  }, [deselectAll]);

  return (
    <GridContainer onClick={handleBackgroundClick}>
      {icons.map(icon => (
        <DesktopIcon
          key={icon.id}
          icon={icon}
          onOpen={handleIconOpen}
          onSelect={handleIconSelect}
        />
      ))}
    </GridContainer>
  );
});

IconGrid.displayName = 'IconGrid';

export default IconGrid;
