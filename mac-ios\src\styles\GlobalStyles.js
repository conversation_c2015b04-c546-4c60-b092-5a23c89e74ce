import { createGlobalStyle } from 'styled-components';

/**
 * Global styles for the macOS/iOS operating system
 * Implements SF Pro font family and base styling
 */
export const GlobalStyles = createGlobalStyle`
  /* SF Pro font family will be loaded via index.html or system fonts */
  
  /* CSS Custom Properties for theme variables */
  :root {
    --font-system: ${props => props.theme?.fonts?.system || 'Inter, -apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", Roboto, Helvetica, Arial, sans-serif'};
    --font-mono: ${props => props.theme?.fonts?.mono || '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace'};

    --color-primary: ${props => props.theme?.colors?.primary || '#007AFF'};
    --color-secondary: ${props => props.theme?.colors?.secondary || '#5856D6'};
    --color-background: ${props => props.theme?.colors?.background || '#FFFFFF'};
    --color-surface: ${props => props.theme?.colors?.surface || '#F2F2F7'};
    --color-text: ${props => props.theme?.colors?.text || '#000000'};
    --color-text-secondary: ${props => props.theme?.colors?.textSecondary || '#6D6D70'};
    --color-border: ${props => props.theme?.colors?.border || '#C6C6C8'};
    --color-accent: ${props => props.theme?.colors?.accent || '#FF9500'};
    --color-success: ${props => props.theme?.colors?.success || '#34C759'};
    --color-warning: ${props => props.theme?.colors?.warning || '#FF9500'};
    --color-error: ${props => props.theme?.colors?.error || '#FF3B30'};
    --color-glass: ${props => props.theme?.colors?.glass || 'rgba(255, 255, 255, 0.8)'};
    --color-glass-strong: ${props => props.theme?.colors?.glassStrong || 'rgba(255, 255, 255, 0.95)'};

    --spacing-xs: ${props => props.theme?.spacing?.xs || '0.25rem'};
    --spacing-sm: ${props => props.theme?.spacing?.sm || '0.5rem'};
    --spacing-md: ${props => props.theme?.spacing?.md || '1rem'};
    --spacing-lg: ${props => props.theme?.spacing?.lg || '1.5rem'};
    --spacing-xl: ${props => props.theme?.spacing?.xl || '2rem'};

    --radius-sm: ${props => props.theme?.borderRadius?.sm || '0.375rem'};
    --radius-md: ${props => props.theme?.borderRadius?.md || '0.5rem'};
    --radius-lg: ${props => props.theme?.borderRadius?.lg || '0.75rem'};
    --radius-xl: ${props => props.theme?.borderRadius?.xl || '1rem'};

    --shadow-sm: ${props => props.theme?.shadows?.sm || '0 1px 2px 0 rgba(0, 0, 0, 0.05)'};
    --shadow-md: ${props => props.theme?.shadows?.md || '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'};
    --shadow-lg: ${props => props.theme?.shadows?.lg || '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'};
    --shadow-xl: ${props => props.theme?.shadows?.xl || '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'};
  }

  /* Reset and base styles */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    font-family: var(--font-system);
    background-color: var(--color-background);
    color: var(--color-text);
    line-height: 1.5;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* Scrollbar styling for webkit browsers */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }

  /* Focus styles */
  button:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  /* Button reset */
  button {
    background: none;
    border: none;
    cursor: pointer;
    font-family: inherit;
  }

  /* Input reset */
  input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    border: none;
    background: transparent;
  }

  /* Link reset */
  a {
    color: inherit;
    text-decoration: none;
  }

  /* List reset */
  ul, ol {
    list-style: none;
  }

  /* Image reset */
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  /* Utility classes */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .no-select {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes scaleIn {
    from {
      transform: scale(0.9);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Frosted glass effect utility */
  .glass {
    background: var(--color-glass);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-strong {
    background: var(--color-glass-strong);
    backdrop-filter: blur(40px);
    -webkit-backdrop-filter: blur(40px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  /* Responsive design helpers */
  @media (max-width: 768px) {
    html {
      font-size: 14px;
    }
  }

  @media (max-width: 480px) {
    html {
      font-size: 12px;
    }
  }

  /* High DPI display optimizations */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
      -webkit-font-smoothing: subpixel-antialiased;
    }
  }

  /* Dark mode specific adjustments */
  @media (prefers-color-scheme: dark) {
    ::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
    }

    ::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  /* Reduce motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
`;
