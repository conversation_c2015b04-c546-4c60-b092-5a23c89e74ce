import React, { memo, useCallback, useState } from 'react';
import styled from 'styled-components';
import { Text } from '../../../styles/components';

/**
 * Styled components for desktop icons
 */
const IconContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  pointer-events: auto;
  transition: transform 0.2s ease;
  position: relative;

  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
`;

const IconWrapper = styled.div`
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
  background: ${props => props.gradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};
  box-shadow: ${props => props.theme?.shadows?.md || '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'};
  margin-bottom: ${props => props.theme?.spacing?.sm || '0.5rem'};
  color: white;
  font-size: 1.5rem;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: inherit;
  }

  ${props => props.selected && `
    box-shadow: 0 0 0 2px ${props.theme?.colors?.primary || '#007AFF'}, ${props.theme?.shadows?.md || '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'};
  `}

  @media (max-width: 768px) {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }
`;

const IconLabel = styled(Text)`
  font-size: 0.75rem;
  color: white;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.3);
  padding: ${props => props.theme?.spacing?.xs || '0.25rem'} ${props => props.theme?.spacing?.sm || '0.5rem'};
  border-radius: ${props => props.theme?.borderRadius?.sm || '0.375rem'};
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  max-width: 5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  ${props => props.selected && `
    background: ${props.theme?.colors?.primary || '#007AFF'}aa;
  `}

  @media (max-width: 768px) {
    font-size: 0.625rem;
    max-width: 4rem;
  }
`;

const SelectionOverlay = styled.div`
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid ${props => props.theme?.colors?.primary || '#007AFF'};
  border-radius: 1.125rem;
  background: ${props => props.theme?.colors?.primary || '#007AFF'}11;
  pointer-events: none;
  opacity: ${props => props.visible ? 1 : 0};
  transition: opacity 0.2s ease;
`;

/**
 * DesktopIcon component with selection and interaction support
 * 
 * @param {Object} props
 * @param {Object} props.icon - Icon data object
 * @param {Function} props.onOpen - Callback when icon is double-clicked
 * @param {Function} props.onSelect - Callback when icon is selected
 */
const DesktopIcon = memo(({ icon, onOpen, onSelect }) => {
  const [clickCount, setClickCount] = useState(0);
  const [clickTimer, setClickTimer] = useState(null);

  const IconComponent = icon.icon;

  // Handle click events (single vs double click)
  const handleClick = useCallback((e) => {
    e.stopPropagation();
    
    const isMultiSelect = e.metaKey || e.ctrlKey;
    
    setClickCount(prev => prev + 1);
    
    if (clickTimer) {
      clearTimeout(clickTimer);
    }

    const timer = setTimeout(() => {
      if (clickCount === 0) {
        // Single click - select icon
        onSelect(icon.id, isMultiSelect);
      } else if (clickCount === 1) {
        // Double click - open app
        onOpen(icon.id);
      }
      setClickCount(0);
    }, 300);

    setClickTimer(timer);
  }, [clickCount, clickTimer, icon.id, onOpen, onSelect]);

  // Handle context menu (right click)
  const handleContextMenu = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Select icon if not already selected
    if (!icon.selected) {
      onSelect(icon.id, false);
    }
    
    // TODO: Show context menu
    console.log('Context menu for:', icon.name);
  }, [icon.id, icon.selected, onSelect]);

  // Cleanup timer on unmount
  React.useEffect(() => {
    return () => {
      if (clickTimer) {
        clearTimeout(clickTimer);
      }
    };
  }, [clickTimer]);

  return (
    <IconContainer
      onClick={handleClick}
      onContextMenu={handleContextMenu}
      style={{
        gridColumn: icon.position.x + 1,
        gridRow: icon.position.y + 1
      }}
    >
      <IconWrapper
        gradient={icon.color}
        selected={icon.selected}
      >
        <IconComponent />
        <SelectionOverlay visible={icon.selected} />
      </IconWrapper>
      
      <IconLabel selected={icon.selected}>
        {icon.name}
      </IconLabel>
    </IconContainer>
  );
});

DesktopIcon.displayName = 'DesktopIcon';

export default DesktopIcon;
