import { useState, useCallback, useMemo } from 'react';
import styled from 'styled-components';
import { 
  RiSearchLine, 
  RiArrowLeftSLine, 
  RiArrowRightSLine,
  RiApps2Line,
  RiListUnordered,
  RiComputerLine,
  RiDownloadLine,
  RiFileLine,
  RiFileTextLine,
  RiFolderLine,
  RiImageLine,
  RiVideoLine,
  RiMusicLine,
  RiArchiveLine,
  RiCodeLine,
  RiAddLine,
  RiDeleteBinLine,
  RiEditLine,
  RiEyeLine
} from 'react-icons/ri';
import { Input, Button, Flex, Text } from '../../styles/components';
import { useFileSystemStore } from './store/fileSystemStore';

/**
 * Styled components for the File Explorer
 */
const ExplorerContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  background: ${props => props.theme?.colors?.background || '#FFFFFF'};
`;

const ExplorerToolbar = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
  gap: ${props => props.theme.spacing.sm};
`;

const SearchContainer = styled.div`
  position: relative;
  flex: 1;
  max-width: 300px;
`;

const SearchInput = styled(Input)`
  padding-right: 2.5rem;
`;

const SearchIcon = styled(RiSearchLine)`
  position: absolute;
  right: ${props => props.theme.spacing.sm};
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.textSecondary};
  pointer-events: none;
`;

const ExplorerContent = styled.div`
  flex: 1;
  display: flex;
  height: calc(100% - 3rem);
`;

const Sidebar = styled.div`
  width: 200px;
  background: ${props => props.theme.colors.surface};
  border-right: 1px solid ${props => props.theme.colors.border};
  padding: ${props => props.theme.spacing.md};
  overflow-y: auto;
`;

const SidebarSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const SidebarTitle = styled(Text)`
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const SidebarItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.background};
  }

  ${props => props.active && `
    background: ${props.theme.colors.primary};
    color: white;
  `}
`;

const MainContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const PathBar = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme?.spacing?.sm || '0.5rem'} ${props => props.theme?.spacing?.md || '1rem'};
  background: ${props => props.theme?.colors?.surface || '#F2F2F7'};
  border-bottom: 1px solid ${props => props.theme?.colors?.border || '#C6C6C8'};
  gap: ${props => props.theme?.spacing?.xs || '0.25rem'};
`;

const PathSegment = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme?.colors?.text || '#000000'};
  cursor: pointer;
  padding: ${props => props.theme?.spacing?.xs || '0.25rem'} ${props => props.theme?.spacing?.sm || '0.5rem'};
  border-radius: ${props => props.theme?.borderRadius?.sm || '0.375rem'};
  transition: background-color 0.2s ease;

  &:hover {
    background: ${props => props.theme?.colors?.background || '#FFFFFF'};
  }

  &:after {
    content: '/';
    margin-left: ${props => props.theme?.spacing?.xs || '0.25rem'};
    color: ${props => props.theme?.colors?.textSecondary || '#6D6D70'};
  }

  &:last-child:after {
    content: '';
  }
`;

const FileGrid = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing.md};
  overflow: auto;
  
  ${props => props.viewMode === 'grid' && `
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: ${props.theme.spacing.md};
  `}

  ${props => props.viewMode === 'list' && `
    display: flex;
    flex-direction: column;
    gap: ${props.theme.spacing.xs};
  `}
`;

const FileItem = styled.div`
  display: flex;
  flex-direction: ${props => props.viewMode === 'grid' ? 'column' : 'row'};
  align-items: ${props => props.viewMode === 'grid' ? 'center' : 'flex-start'};
  padding: ${props => props.theme?.spacing?.sm || '0.5rem'};
  border-radius: ${props => props.theme?.borderRadius?.md || '0.5rem'};
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: 2px solid transparent;

  &:hover {
    background: ${props => props.theme?.colors?.surface || '#F2F2F7'};
  }

  ${props => props.selected && `
    background: ${props.theme?.colors?.primary || '#007AFF'}22;
    border-color: ${props.theme?.colors?.primary || '#007AFF'};
  `}

  ${props => props.viewMode === 'list' && `
    padding: ${props.theme?.spacing?.xs || '0.25rem'} ${props.theme?.spacing?.sm || '0.5rem'};
    gap: ${props.theme?.spacing?.sm || '0.5rem'};
  `}
`;

const FileIcon = styled.div`
  width: ${props => props.viewMode === 'grid' ? '3rem' : '2rem'};
  height: ${props => props.viewMode === 'grid' ? '3rem' : '2rem'};
  background: ${props => props.color || props.theme.colors.primary}22;
  border-radius: ${props => props.theme.borderRadius.md};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: ${props => props.viewMode === 'grid' ? props.theme.spacing.sm : '0'};
  color: ${props => props.color || props.theme.colors.primary};
  font-size: ${props => props.viewMode === 'grid' ? '1.5rem' : '1rem'};
  flex-shrink: 0;
`;

const FileInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: ${props => props.viewMode === 'grid' ? 'center' : 'flex-start'};
  flex: 1;
  min-width: 0;
`;

const FileName = styled(Text)`
  font-size: ${props => props.viewMode === 'grid' ? '0.75rem' : '0.875rem'};
  text-align: ${props => props.viewMode === 'grid' ? 'center' : 'left'};
  color: ${props => props.theme?.colors?.text || '#000000'};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
`;

const FileDetails = styled(Text)`
  font-size: 0.75rem;
  color: ${props => props.theme?.colors?.textSecondary || '#6D6D70'};
  margin-top: ${props => props.theme?.spacing?.xs || '0.25rem'};

  ${props => props.viewMode === 'grid' && `
    display: none;
  `}
`;

const ContextMenu = styled.div`
  position: fixed;
  background: ${props => props.theme?.colors?.surface || '#F2F2F7'};
  border: 1px solid ${props => props.theme?.colors?.border || '#C6C6C8'};
  border-radius: ${props => props.theme?.borderRadius?.md || '0.5rem'};
  box-shadow: ${props => props.theme?.shadows?.lg || '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'};
  padding: ${props => props.theme?.spacing?.xs || '0.25rem'};
  z-index: 1000;
  min-width: 150px;
`;

const ContextMenuItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme?.spacing?.sm || '0.5rem'};
  padding: ${props => props.theme?.spacing?.sm || '0.5rem'};
  border-radius: ${props => props.theme?.borderRadius?.sm || '0.375rem'};
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: ${props => props.theme?.colors?.background || '#FFFFFF'};
  }

  ${props => props.danger && `
    color: ${props.theme?.colors?.error || '#FF3B30'};
  `}
`;

/**
 * File Explorer component with comprehensive file management
 * Provides tree-view navigation and CRUD operations
 */
const FileExplorer = () => {
  const {
    currentPath,
    files,
    selectedFiles,
    viewMode,
    searchQuery,
    navigateToPath,
    selectFile,
    deselectAll,
    setViewMode,
    setSearchQuery,
    createFile,
    createFolder,
    deleteFile,
    renameFile
  } = useFileSystemStore();

  const [contextMenu, setContextMenu] = useState(null);

  // Get file icon based on type
  const getFileIcon = useCallback((file) => {
    if (file.type === 'folder') return RiFolderLine;
    
    const extension = file.name.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'txt':
      case 'md':
      case 'doc':
      case 'docx':
        return RiFileTextLine;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
        return RiImageLine;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'mkv':
        return RiVideoLine;
      case 'mp3':
      case 'wav':
      case 'flac':
        return RiMusicLine;
      case 'zip':
      case 'rar':
      case '7z':
        return RiArchiveLine;
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
      case 'html':
      case 'css':
        return RiCodeLine;
      default:
        return RiFileLine;
    }
  }, []);

  // Get file color based on type
  const getFileColor = useCallback((file) => {
    if (file.type === 'folder') return '#3B82F6';
    
    const extension = file.name.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'txt':
      case 'md':
      case 'doc':
      case 'docx':
        return '#6B7280';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
        return '#10B981';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'mkv':
        return '#8B5CF6';
      case 'mp3':
      case 'wav':
      case 'flac':
        return '#F59E0B';
      case 'zip':
      case 'rar':
      case '7z':
        return '#EF4444';
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
      case 'html':
      case 'css':
        return '#06B6D4';
      default:
        return '#6B7280';
    }
  }, []);

  // Filter files based on search query
  const filteredFiles = useMemo(() => {
    if (!searchQuery) return files;
    return files.filter(file =>
      file.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [files, searchQuery]);

  // Handle file double-click
  const handleFileDoubleClick = useCallback((file) => {
    if (file.type === 'folder') {
      navigateToPath(`${currentPath}/${file.name}`);
    } else {
      // Open file (could trigger different apps based on file type)
      console.log('Opening file:', file.name);
    }
  }, [currentPath, navigateToPath]);

  // Handle file selection
  const handleFileClick = useCallback((file, event) => {
    const isMultiSelect = event.ctrlKey || event.metaKey;
    selectFile(file.id, isMultiSelect);
  }, [selectFile]);

  // Handle context menu
  const handleContextMenu = useCallback((event, file) => {
    event.preventDefault();
    
    if (file && !selectedFiles.includes(file.id)) {
      selectFile(file.id, false);
    }
    
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      file
    });
  }, [selectedFiles, selectFile]);

  // Close context menu
  const closeContextMenu = useCallback(() => {
    setContextMenu(null);
  }, []);

  // Handle context menu actions
  const handleContextAction = useCallback((action, file) => {
    switch (action) {
      case 'new-folder':
        createFolder('New Folder');
        break;
      case 'new-file':
        createFile('New File.txt');
        break;
      case 'rename':
        if (file) {
          const newName = prompt('Enter new name:', file.name);
          if (newName && newName !== file.name) {
            renameFile(file.id, newName);
          }
        }
        break;
      case 'delete':
        if (file && confirm(`Are you sure you want to delete ${file.name}?`)) {
          deleteFile(file.id);
        }
        break;
      case 'view':
        console.log('View file:', file?.name);
        break;
    }
    closeContextMenu();
  }, [createFolder, createFile, renameFile, deleteFile, closeContextMenu]);

  // Generate path segments for navigation
  const pathSegments = useMemo(() => {
    return currentPath.split('/').filter(Boolean);
  }, [currentPath]);

  // Navigate to path segment
  const navigateToSegment = useCallback((index) => {
    const newPath = '/' + pathSegments.slice(0, index + 1).join('/');
    navigateToPath(newPath);
  }, [pathSegments, navigateToPath]);

  return (
    <ExplorerContainer onClick={closeContextMenu}>
      <ExplorerToolbar>
        <Button variant="ghost" size="sm">
          <RiArrowLeftSLine />
        </Button>
        <Button variant="ghost" size="sm">
          <RiArrowRightSLine />
        </Button>
        
        <SearchContainer>
          <SearchInput
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <SearchIcon />
        </SearchContainer>

        <Button 
          variant={viewMode === 'grid' ? 'secondary' : 'ghost'} 
          size="sm"
          onClick={() => setViewMode('grid')}
        >
          <RiApps2Line />
        </Button>
        <Button 
          variant={viewMode === 'list' ? 'secondary' : 'ghost'} 
          size="sm"
          onClick={() => setViewMode('list')}
        >
          <RiListUnordered />
        </Button>
      </ExplorerToolbar>

      <ExplorerContent>
        <Sidebar>
          <SidebarSection>
            <SidebarTitle>Favorites</SidebarTitle>
            <SidebarItem active={currentPath === '/Desktop'} onClick={() => navigateToPath('/Desktop')}>
              <RiComputerLine size={16} />
              <Text size="sm">Desktop</Text>
            </SidebarItem>
            <SidebarItem active={currentPath === '/Downloads'} onClick={() => navigateToPath('/Downloads')}>
              <RiDownloadLine size={16} />
              <Text size="sm">Downloads</Text>
            </SidebarItem>
            <SidebarItem active={currentPath === '/Documents'} onClick={() => navigateToPath('/Documents')}>
              <RiFileLine size={16} />
              <Text size="sm">Documents</Text>
            </SidebarItem>
          </SidebarSection>
        </Sidebar>

        <MainContent>
          <PathBar>
            <PathSegment onClick={() => navigateToPath('/')}>
              Home
            </PathSegment>
            {pathSegments.map((segment, index) => (
              <PathSegment
                key={index}
                onClick={() => navigateToSegment(index)}
              >
                {segment}
              </PathSegment>
            ))}
          </PathBar>

          <FileGrid 
            viewMode={viewMode}
            onContextMenu={(e) => handleContextMenu(e, null)}
          >
            {filteredFiles.map(file => {
              const IconComponent = getFileIcon(file);
              const isSelected = selectedFiles.includes(file.id);
              
              return (
                <FileItem
                  key={file.id}
                  viewMode={viewMode}
                  selected={isSelected}
                  onClick={(e) => handleFileClick(file, e)}
                  onDoubleClick={() => handleFileDoubleClick(file)}
                  onContextMenu={(e) => handleContextMenu(e, file)}
                >
                  <FileIcon 
                    viewMode={viewMode}
                    color={getFileColor(file)}
                  >
                    <IconComponent />
                  </FileIcon>
                  <FileInfo viewMode={viewMode}>
                    <FileName viewMode={viewMode}>{file.name}</FileName>
                    <FileDetails viewMode={viewMode}>
                      {file.type === 'folder' ? 'Folder' : `${file.size || '0 KB'} • ${file.modified || 'Today'}`}
                    </FileDetails>
                  </FileInfo>
                </FileItem>
              );
            })}
          </FileGrid>
        </MainContent>
      </ExplorerContent>

      {contextMenu && (
        <ContextMenu
          style={{
            left: contextMenu.x,
            top: contextMenu.y
          }}
        >
          <ContextMenuItem onClick={() => handleContextAction('new-folder')}>
            <RiAddLine size={16} />
            <Text size="sm">New Folder</Text>
          </ContextMenuItem>
          <ContextMenuItem onClick={() => handleContextAction('new-file')}>
            <RiAddLine size={16} />
            <Text size="sm">New File</Text>
          </ContextMenuItem>
          {contextMenu.file && (
            <>
              <ContextMenuItem onClick={() => handleContextAction('view', contextMenu.file)}>
                <RiEyeLine size={16} />
                <Text size="sm">View</Text>
              </ContextMenuItem>
              <ContextMenuItem onClick={() => handleContextAction('rename', contextMenu.file)}>
                <RiEditLine size={16} />
                <Text size="sm">Rename</Text>
              </ContextMenuItem>
              <ContextMenuItem 
                danger 
                onClick={() => handleContextAction('delete', contextMenu.file)}
              >
                <RiDeleteBinLine size={16} />
                <Text size="sm">Delete</Text>
              </ContextMenuItem>
            </>
          )}
        </ContextMenu>
      )}
    </ExplorerContainer>
  );
};

export default FileExplorer;
