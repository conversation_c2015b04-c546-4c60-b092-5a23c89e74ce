{"name": "mac-ios", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "coverage": "vitest run --coverage", "type-check": "tsc --noEmit"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "remixicon": "^4.6.0", "tailwindcss": "^4.1.10", "styled-components": "^6.1.19", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react-swc": "^3.9.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.0.0", "vite": "^6.3.5", "vitest": "^3.2.4"}}