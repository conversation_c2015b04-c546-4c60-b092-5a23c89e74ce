import { memo, useState, useCallback, useRef } from 'react';
import styled from 'styled-components';
import { useDockStore, useWindowStore } from '../../../store';
import DockIcon from './DockIcon';
import { glassEffect } from '../../../styles/components';

/**
 * Styled components for the dock
 */
const DockContainer = styled.div`
  position: fixed;
  bottom: ${props => props.theme?.spacing?.md || '1rem'};
  left: 50%;
  transform: translateX(-50%);
  ${glassEffect}
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  border-radius: 1.5rem;
  padding: ${props => props.theme?.spacing?.sm || '0.5rem'};
  box-shadow: ${props => props.theme?.shadows?.xl || '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'};
  z-index: 900;
  display: flex;
  align-items: end;
  gap: ${props => props.theme?.spacing?.xs || '0.25rem'};
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* Hover effect for the entire dock */
  &:hover {
    transform: translateX(-50%) translateY(-0.25rem);
    box-shadow: ${props => props.theme?.shadows?.xl || '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'}, 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  @media (max-width: 768px) {
    bottom: ${props => props.theme.spacing.sm};
    padding: ${props => props.theme.spacing.xs};
    gap: ${props => props.theme.spacing.xs};
    border-radius: 1rem;
  }
`;

const DockSeparator = styled.div`
  width: 1px;
  height: 2rem;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 ${props => props.theme.spacing.xs};
  border-radius: 0.5px;

  @media (max-width: 768px) {
    height: 1.5rem;
  }
`;

/**
 * Enhanced Dock component with magnification effects and smooth animations
 * Displays app icons with running indicators and hover effects
 */
const Dock = memo(() => {
  const { apps, setHoveredApp } = useDockStore();
  const { openWindow, windows } = useWindowStore();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const dockRef = useRef(null);

  // Handle mouse movement for magnification effect
  const handleMouseMove = useCallback((e) => {
    if (!dockRef.current) return;

    const rect = dockRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    setMousePosition({ x, y });
  }, []);

  // Handle mouse leave to reset magnification
  const handleMouseLeave = useCallback(() => {
    setHoveredApp(null);
    setMousePosition({ x: 0, y: 0 });
  }, [setHoveredApp]);

  // Handle app click to open/focus window
  const handleAppClick = useCallback((app) => {
    // Check if app already has a window open
    const existingWindow = windows.find(w => w.appId === app.id);

    if (existingWindow) {
      if (existingWindow.minimized) {
        // Restore minimized window
        // This would be handled by the window store
        console.log('Restore window:', app.name);
      } else {
        // Bring window to front or minimize if already active
        console.log('Focus or minimize window:', app.name);
      }
    } else {
      // Open new window
      openWindow(app.id, app.name);
    }
  }, [windows, openWindow]);

  // Calculate magnification based on mouse position
  const calculateMagnification = useCallback((iconRect) => {
    if (!mousePosition.x || !mousePosition.y) return 1;

    const iconCenterX = iconRect.left + iconRect.width / 2;
    const distance = Math.abs(mousePosition.x - iconCenterX);
    const maxDistance = 100; // Maximum distance for magnification effect
    const maxScale = 1.5; // Maximum scale factor

    if (distance > maxDistance) return 1;

    const scale = 1 + (maxScale - 1) * (1 - distance / maxDistance);
    return Math.max(1, scale);
  }, [mousePosition]);

  // Separate system apps from user apps (for potential separator)
  const systemApps = apps.filter(app => app.category === 'system');
  const userApps = apps.filter(app => app.category !== 'system');
  const showSeparator = systemApps.length > 0 && userApps.length > 0;

  return (
    <DockContainer
      ref={dockRef}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      {/* User apps */}
      {userApps.map((app, index) => (
        <DockIcon
          key={app.id}
          app={app}
          index={index}
          onClick={() => handleAppClick(app)}
          onHover={(appId) => setHoveredApp(appId)}
          calculateMagnification={calculateMagnification}
          mousePosition={mousePosition}
        />
      ))}

      {/* Separator between user and system apps */}
      {showSeparator && <DockSeparator />}

      {/* System apps */}
      {systemApps.map((app, index) => (
        <DockIcon
          key={app.id}
          app={app}
          index={userApps.length + index + (showSeparator ? 1 : 0)}
          onClick={() => handleAppClick(app)}
          onHover={(appId) => setHoveredApp(appId)}
          calculateMagnification={calculateMagnification}
          mousePosition={mousePosition}
        />
      ))}
    </DockContainer>
  );
});

Dock.displayName = 'Dock';

export default Dock;
